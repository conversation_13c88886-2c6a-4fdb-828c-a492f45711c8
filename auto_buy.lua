local WindUI = loadstring(game:HttpGet("https://github.com/Footagesus/WindUI/releases/latest/download/main.lua"))()


local Window = WindUI:CreateWindow({
    Title = "WindUI Library",
    Icon = "rbxassetid://129260712070622",
    IconThemed = true,
    Author = "Example UI",
    Folder = "CloudHub",
    Size = UDim2.fromOffset(580, 460),
    Transparent = true,
    Theme = "Dark",
    User = {
        Enabled = true, -- <- or false
        Callback = function() print("clicked") end, -- <- optional
        Anonymous = true -- <- or true
    },
    SideBarWidth = 200,
    -- HideSearchBar = true, -- hides searchbar
    ScrollBarEnabled = true, -- enables scrollbar
    -- Background = "rbxassetid://13511292247", -- rbxassetid only

    -- remove it below if you don't want to use the key system in your script.
    KeySystem = { -- <- keysystem enabled
        Key = { "1234", "5678" },
        Note = "Example Key System. \n\nThe Key is '1234' or '5678",
        -- Thumbnail = {
        --     Image = "rbxassetid://18220445082", -- rbxassetid only
        --     Title = "Thumbnail"
        -- },
        URL = "link-to-linkvertise-or-discord-or-idk", -- remove this if the key is not obtained from the link.
        SaveKey = true, -- saves key : optional
    },
})

local Tabs = {}

do
    Tabs.AutoBuyTab = Window:Tab({ Title = "Auto Buy", Icon = "file-cog" })
end

-- create a feature to auto buy items using checkboxes and a button to start the script

local items = {
    "Item 1",
    "Item 2",
    "Item 3",
    "Item 4",
    "Item 5",
}

local selectedItems = {}
local item1Price = 0 -- Default price for Item 1
local item1PriceInput -- Will hold the price input element for Item 1

-- add start button
local startButton = Tabs.AutoBuyTab:Button({
    Title = "Start",
    Callback = function()
        print("Starting...")
        print("Selected items: " .. table.concat(selectedItems, ", "))
    end, -- <- optional callback function
    Enabled = false, -- <- optional enabled state
})




-- Special handling for Item 1 with price configuration
Tabs.AutoBuyTab:Toggle({
    Title = items[1], -- "Item 1"
    Callback = function(state)
        if state then
            table.insert(selectedItems, items[1])
            -- Show price input when Item 1 is enabled
            if item1PriceInput then
                item1PriceInput:SetVisible(true)
            end
        else
            -- Remove Item 1 from selected items
            for i, selectedItem in ipairs(selectedItems) do
                if selectedItem == items[1] then
                    table.remove(selectedItems, i)
                    break
                end
            end
            -- Hide price input when Item 1 is disabled
            if item1PriceInput then
                item1PriceInput:SetVisible(false)
            end
        end
    end
})

-- Price input for Item 1 (initially hidden)
item1PriceInput = Tabs.AutoBuyTab:Input({
    Title = "Item 1 Price",
    Placeholder = "Enter price for Item 1",
    Callback = function(value)
        local price = tonumber(value)
        if price and price > 0 then
            item1Price = price
            print("Item 1 price set to: " .. item1Price)
        else
            print("Invalid price entered for Item 1")
        end
    end
})

-- Initially hide the price input
item1PriceInput:SetVisible(false)

-- Handle remaining items (Item 2-5) normally
for i = 2, #items do
    local item = items[i]
    Tabs.AutoBuyTab:Toggle({
        Title = item,
        Callback = function(state)
            if state then
                table.insert(selectedItems, item)
            else
                -- Remove item from selected items
                for j, selectedItem in ipairs(selectedItems) do
                    if selectedItem == item then
                        table.remove(selectedItems, j)
                        break
                    end
                end
            end
        end
    })
end
