local WindUI = loadstring(game:HttpGet("https://github.com/Footagesus/WindUI/releases/latest/download/main.lua"))()


local Window = WindUI:CreateWindow({
    Title = "WindUI Library",
    Icon = "rbxassetid://129260712070622",
    IconThemed = true,
    Author = "Example UI",
    Folder = "CloudHub",
    Size = UDim2.fromOffset(580, 460),
    Transparent = true,
    Theme = "Dark",
    User = {
        Enabled = true, -- <- or false
        Callback = function() print("clicked") end, -- <- optional
        Anonymous = true -- <- or true
    },
    SideBarWidth = 200,
    -- HideSearchBar = true, -- hides searchbar
    ScrollBarEnabled = true, -- enables scrollbar
    -- Background = "rbxassetid://13511292247", -- rbxassetid only

    -- remove it below if you don't want to use the key system in your script.
    KeySystem = { -- <- keysystem enabled
        Key = { "1234", "5678" },
        Note = "Example Key System. \n\nThe Key is '1234' or '5678",
        -- Thumbnail = {
        --     Image = "rbxassetid://18220445082", -- rbxassetid only
        --     Title = "Thumbnail"
        -- },
        URL = "link-to-linkvertise-or-discord-or-idk", -- remove this if the key is not obtained from the link.
        SaveKey = true, -- saves key : optional
    },
})

local Tabs = {}

do
    Tabs.AutoBuyTab = Window:Tab({ Title = "Auto Buy", Icon = "file-cog" })
end

-- create a feature to auto buy items using checkboxes and a button to start the script

local items = {
    { name = "ArcticStaffRed", price = 1000000 },
    { name = "Item 2", price = 250 },
    { name = "Item 3", price = 500 },
    { name = "Item 4", price = 750 },
    { name = "Item 5", price = 1000 },
}

local selectedItems = {} -- Will store selected item objects with name and price
local isRunning = false -- Flag to control the auto-buy loop
local autoBuyConnection -- Connection for the auto-buy loop
local searchCount = 0 -- Counter for search attempts

-- Function to attempt purchasing an item
local function attemptPurchase(shopItem, selectedItem, shopPrice)
    -- This is where you would implement the actual purchase logic
    -- The exact implementation depends on how the game handles purchases

    print("🛒 PURCHASING: " .. selectedItem.name .. " for " .. shopPrice)

    -- Example purchase logic (you may need to modify this based on the game's API):
    -- This might involve firing a RemoteEvent or calling a specific function

    -- For now, we'll just log the purchase attempt
    print("✅ Successfully purchased " .. selectedItem.name .. " for " .. shopPrice)

    -- You might want to add actual purchase code here, such as:
    -- local purchaseEvent = replicatedStorage:FindFirstChild("PurchaseEvent")
    -- if purchaseEvent then
    --     purchaseEvent:FireServer(shopItem, shopPrice)
    -- end
end

-- Function to search for items in PlayerShop and check prices
local function searchPlayerShop()
    local replicatedStorage = game:GetService("ReplicatedStorage")
    local playerShop = replicatedStorage:FindFirstChild("PlayerShops")

    if not playerShop then
        print("❌ PlayerShop not found in ReplicatedStorage")
        return
    end

    searchCount = searchCount + 1

    -- Print status every 10 searches (every 10 seconds) to avoid spam
    if searchCount % 10 == 0 then
        print("🔍 Search #" .. searchCount .. " - Still looking for items...")
    end

    -- Loop through all children of PlayerShop
    for _, shopChild in pairs(playerShop:GetChildren()) do
        local sellShop = shopChild:FindFirstChild("SellShop")
        if sellShop then
            local items = sellShop:FindFirstChild("Items")
            if items then
                -- Loop through all items in this shop
                for _, shopItem in pairs(items:GetChildren()) do
                    -- Check if this shop item matches any of our selected items
                    for _, selectedItem in ipairs(selectedItems) do
                        if shopItem.Name == selectedItem.name then
                            local askingPrice = shopItem:FindFirstChild("AskingPrice")
                            if askingPrice and askingPrice.Value then
                                local shopPrice = askingPrice.Value
                                print("Found " .. selectedItem.name .. " - Shop Price: " .. shopPrice .. ", Target Price: " .. selectedItem.price)

                                -- Check if the shop price matches our target price
                                if shopPrice <= selectedItem.price then
                                    print("💰 Price match! Attempting to buy " .. selectedItem.name)
                                    -- Call attemptPurchase function (now properly defined above)
                                    attemptPurchase(shopItem, selectedItem, shopPrice)
                                else
                                    print("💸 Price too high for " .. selectedItem.name .. " (Shop: " .. shopPrice .. ", Max: " .. selectedItem.price .. ")")
                                end
                            end
                        end
                    end
                end
            end
        end
    end
end

-- add start button
local startButton = Tabs.AutoBuyTab:Button({
    Title = "Start Auto Buy",
    Callback = function()
        if isRunning then
            print("Auto-buy is already running!")
            return
        end

        if #selectedItems == 0 then
            print("No items selected!")
            return
        end

        print("🚀 Starting Auto Buy...")
        print("📊 Selected items and target prices:")
        for _, item in ipairs(selectedItems) do
            print("  • " .. item.name .. ": " .. item.price)
        end
        print("🔍 Will search PlayerShop every second for matching items...")

        isRunning = true
        searchCount = 0 -- Reset search counter

        -- Start the auto-buy loop with a timer
        local lastSearchTime = 0
        local runService = game:GetService("RunService")
        autoBuyConnection = runService.Heartbeat:Connect(function()
            if isRunning then
                local currentTime = tick()
                if currentTime - lastSearchTime >= 1 then -- Search every 1 second
                    searchPlayerShop()
                    lastSearchTime = currentTime
                end
            end
        end)

        print("✅ Auto-buy started! Searching every second...")
    end,
})

-- add stop button
local stopButton = Tabs.AutoBuyTab:Button({
    Title = "Stop Auto Buy",
    Callback = function()
        if not isRunning then
            print("Auto-buy is not running!")
            return
        end

        print("🛑 Stopping Auto Buy...")
        isRunning = false

        if autoBuyConnection then
            autoBuyConnection:Disconnect()
            autoBuyConnection = nil
        end

        print("✅ Auto-buy stopped!")
    end,
})

-- Status is displayed through console messages
print("🔧 Auto Buy System Ready!")
print("📋 Configure your items, select them, and click 'Start Auto Buy' to begin.")

-- Create toggles for all items with their configured prices
for _, item in ipairs(items) do
    Tabs.AutoBuyTab:Toggle({
        Title = item.name .. " (Price: " .. item.price .. ")",
        Callback = function(state)
            if state then
                table.insert(selectedItems, item)
                print("Selected: " .. item.name .. " at price " .. item.price)
            else
                -- Remove item from selected items
                for i, selectedItem in ipairs(selectedItems) do
                    if selectedItem.name == item.name then
                        table.remove(selectedItems, i)
                        print("Deselected: " .. item.name)
                        break
                    end
                end
            end
        end
    })
end




