local WindUI = loadstring(game:HttpGet("https://github.com/Footagesus/WindUI/releases/latest/download/main.lua"))()


local Window = WindUI:CreateWindow({
    Title = "WindUI Library",
    Icon = "rbxassetid://129260712070622",
    IconThemed = true,
    Author = "Example UI",
    Folder = "CloudHub",
    Size = UDim2.fromOffset(580, 460),
    Transparent = true,
    Theme = "Dark",
    User = {
        Enabled = true, -- <- or false
        Callback = function() print("clicked") end, -- <- optional
        Anonymous = true -- <- or true
    },
    SideBarWidth = 200,
    -- HideSearchBar = true, -- hides searchbar
    ScrollBarEnabled = true, -- enables scrollbar
    -- Background = "rbxassetid://13511292247", -- rbxassetid only

    -- remove it below if you don't want to use the key system in your script.
    KeySystem = { -- <- keysystem enabled
        Key = { "1234", "5678" },
        Note = "Example Key System. \n\nThe Key is '1234' or '5678",
        -- Thumbnail = {
        --     Image = "rbxassetid://18220445082", -- rbxassetid only
        --     Title = "Thumbnail"
        -- },
        URL = "link-to-linkvertise-or-discord-or-idk", -- remove this if the key is not obtained from the link.
        SaveKey = true, -- saves key : optional
    },
})

local Tabs = {}

do
    Tabs.AutoBuyTab = Window:Tab({ Title = "Auto Buy", Icon = "file-cog" })
end

-- create a feature to auto buy items using checkboxes and a button to start the script

local items = {
    { name = "Item 1", price = 100 },
    { name = "Item 2", price = 250 },
    { name = "Item 3", price = 500 },
    { name = "Item 4", price = 750 },
    { name = "Item 5", price = 1000 },
}

local selectedItems = {} -- Will store selected item objects with name and price

-- add start button
local startButton = Tabs.AutoBuyTab:Button({
    Title = "Start",
    Callback = function()
        print("Starting...")

        if #selectedItems == 0 then
            print("No items selected!")
            return
        end

        print("Selected items and prices:")
        local totalCost = 0
        for _, item in ipairs(selectedItems) do
            print("- " .. item.name .. ": " .. item.price)
            totalCost = totalCost + item.price
        end
        print("Total cost: " .. totalCost)
    end, -- <- optional callback function
    Enabled = false, -- <- optional enabled state
})




-- Create toggles for all items with their configured prices
for _, item in ipairs(items) do
    Tabs.AutoBuyTab:Toggle({
        Title = item.name .. " (Price: " .. item.price .. ")",
        Callback = function(state)
            if state then
                table.insert(selectedItems, item)
                print("Selected: " .. item.name .. " at price " .. item.price)
            else
                -- Remove item from selected items
                for i, selectedItem in ipairs(selectedItems) do
                    if selectedItem.name == item.name then
                        table.remove(selectedItems, i)
                        print("Deselected: " .. item.name)
                        break
                    end
                end
            end
        end
    })
end
